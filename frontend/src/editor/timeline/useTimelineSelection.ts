import { useState, useCallback, useRef, useContext, useEffect } from "react";
import { StoreContext } from "../../store";
import { EditorElement, Caption } from "../../types";
import {
  getTimelineContainerWidth,
  timeStringToMs,
  TIMELINE_CONSTANTS,
} from "../../utils/timeUtils";

interface SelectionState {
  isSelecting: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
}

interface UseTimelineSelectionReturn {
  selectionState: SelectionState;
  isSelecting: boolean;
  handleMouseDown: (e: React.MouseEvent) => void;
  handleMouseMove: (e: React.MouseEvent) => void;
  handleMouseUp: (e: React.MouseEvent) => void;
  resetSelection: () => void;
}

const MINIMUM_DRAG_DISTANCE = 5; // 最小拖拽距离阈值

/**
 * 时间线框选功能的自定义Hook
 */
export const useTimelineSelection = (
  containerRef: React.RefObject<HTMLElement>
): UseTimelineSelectionReturn => {
  const store = useContext(StoreContext);
  const [selectionState, setSelectionState] = useState<SelectionState>({
    isSelecting: false,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
  });

  const isDraggingRef = useRef(false);
  const hasMovedRef = useRef(false);

  // 执行框选逻辑
  const performSelection = useCallback(() => {
    if (!containerRef.current) {
      return;
    }

    const containerWidth = getTimelineContainerWidth();

    if (!containerWidth) {
      return;
    }

    // 计算选择框的边界
    const selectionLeft = Math.min(
      selectionState.startX,
      selectionState.currentX
    );
    const selectionRight = Math.max(
      selectionState.startX,
      selectionState.currentX
    );
    const selectionTop = Math.min(
      selectionState.startY,
      selectionState.currentY
    );
    const selectionBottom = Math.max(
      selectionState.startY,
      selectionState.currentY
    );

    // 转换为时间范围
    const { HANDLE_WIDTH } = TIMELINE_CONSTANTS;
    const effectiveWidth = containerWidth;

    const leftPercent = Math.max(
      0,
      (selectionLeft - HANDLE_WIDTH) / effectiveWidth
    );
    const rightPercent = Math.min(
      1,
      (selectionRight - HANDLE_WIDTH) / effectiveWidth
    );

    const visibleStartTime = store.timelinePan.offsetX;
    const selectionStartTime =
      visibleStartTime + leftPercent * store.timelineDisplayDuration;
    const selectionEndTime =
      visibleStartTime + rightPercent * store.timelineDisplayDuration;

    // 查找与选择框相交的元素
    const selectedElements: EditorElement[] = [];
    const selectedCaptions: Caption[] = [];

    // 获取所有轨道及其元素，用于计算垂直位置
    const trackElements = store.trackManager.getAllTrackElements();

    // 计算轨道高度和间距常量
    const TRACK_HEIGHT = 38; // 从TrackView.tsx中获取
    const TRACK_MARGIN = 1; // 轨道间距（my: 0.125 * 8 = 1px）
    const TRACK_TOTAL_HEIGHT = TRACK_HEIGHT + TRACK_MARGIN * 2;
    const TIMELINE_TOP_PADDING = 35; // 从TimeLineListByTrack.tsx中获取

    // 检查时间线元素轨道
    trackElements.forEach((trackData, trackIndex) => {
      const { elements } = trackData;

      // 计算轨道的垂直位置
      const trackTop = TIMELINE_TOP_PADDING + trackIndex * TRACK_TOTAL_HEIGHT;
      const trackBottom = trackTop + TRACK_HEIGHT;

      // 检查选择框是否与轨道在垂直方向相交
      const verticalIntersects = !(
        trackBottom <= selectionTop || trackTop >= selectionBottom
      );

      if (!verticalIntersects) {
        return;
      }

      // 检查轨道中的每个元素
      elements.forEach((element) => {
        // 检查时间范围是否相交
        const elementStart = element.timeFrame.start;
        const elementEnd = element.timeFrame.end;

        const timeIntersects = !(
          elementEnd <= selectionStartTime || elementStart >= selectionEndTime
        );

        // 只有时间和垂直位置都相交才选中元素
        if (timeIntersects && verticalIntersects) {
          selectedElements.push(element);
        }
      });
    });

    // 检查字幕轨道（字幕轨道在所有普通轨道之后）
    if (store.captions && store.captions.length > 0) {
      // 计算字幕轨道的垂直位置
      const CAPTION_HEIGHT = 30; // 从styles.ts中获取
      const CAPTION_MARGIN = 4; // my: 0.5 * 8 = 4px（从CaptionsTrackView.tsx中获取）
      const captionTrackTop =
        TIMELINE_TOP_PADDING +
        trackElements.length * TRACK_TOTAL_HEIGHT +
        CAPTION_MARGIN;
      const captionTrackBottom = captionTrackTop + CAPTION_HEIGHT;

      // 检查选择框是否与字幕轨道在垂直方向相交
      const captionVerticalIntersects = !(
        captionTrackBottom <= selectionTop || captionTrackTop >= selectionBottom
      );

      if (captionVerticalIntersects) {
        // 检查字幕轨道中的每个字幕
        store.captions.forEach((caption) => {
          // 将字幕时间转换为毫秒
          const captionStart = timeStringToMs(caption.startTime);
          const captionEnd = timeStringToMs(caption.endTime);

          const timeIntersects = !(
            captionEnd <= selectionStartTime || captionStart >= selectionEndTime
          );

          // 只有时间和垂直位置都相交才选中字幕
          if (timeIntersects && captionVerticalIntersects) {
            selectedCaptions.push(caption);
          }
        });
      }
    }

    // 更新选中状态
    const totalSelected = selectedElements.length + selectedCaptions.length;

    if (totalSelected > 0) {
      // 设置选中的时间线元素
      if (selectedElements.length > 0) {
        store.setSelectedElements(selectedElements);
      } else {
        // 如果没有时间线元素被选中，清除时间线元素选择
        store.selectedElement = null;
        store.selectedElements = [];
      }

      // 设置选中的字幕
      if (selectedCaptions.length > 0) {
        // 先清除所有字幕选择
        store.captionManager.deselectAllCaptions();
        // 然后选中框选的字幕
        selectedCaptions.forEach((caption) => {
          store.captionManager.addCaptionToSelection(caption.id);
        });
        store.captions = store.captionManager.captions;

        // 如果选中了多个字幕，确保Canvas中的字幕对象不被选中
        if (selectedCaptions.length > 1 && store.canvas) {
          const captionTextObject = (store.captionManager as any)
            .captionTextObject;
          if (
            captionTextObject &&
            store.canvas.getActiveObject() === captionTextObject
          ) {
            store.canvas.discardActiveObject();
            store.canvas.requestRenderAll();
          }
        }
      } else {
        // 如果没有字幕被选中，清除字幕选择
        store.captionManager.deselectAllCaptions();
        store.captions = store.captionManager.captions;
      }

      console.log(
        `框选完成：选中 ${selectedElements.length} 个时间线元素和 ${selectedCaptions.length} 个字幕`
      );
    } else {
      // 如果没有选中任何元素，清除所有选择
      store.clearAllSelections();
    }
  }, [
    selectionState.startX,
    selectionState.startY,
    selectionState.currentX,
    selectionState.currentY,
    containerRef,
    store,
  ]);

  // 重置选择状态
  const resetSelection = useCallback(() => {
    setSelectionState({
      isSelecting: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
    });
    isDraggingRef.current = false;
    hasMovedRef.current = false;
  }, []);

  // 监听Escape键取消框选
  useEffect(() => {
    const handleCancelSelection = () => {
      if (isDraggingRef.current || selectionState.isSelecting) {
        resetSelection();
      }
    };

    window.addEventListener("timeline-cancel-selection", handleCancelSelection);

    return () => {
      window.removeEventListener(
        "timeline-cancel-selection",
        handleCancelSelection
      );
    };
  }, [resetSelection, selectionState.isSelecting]);

  // 全局鼠标事件监听，支持在容器外继续框选
  useEffect(() => {
    if (!isDraggingRef.current) {
      return;
    }

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (!isDraggingRef.current || !containerRef.current) {
        return;
      }

      const rect = containerRef.current.getBoundingClientRect();
      const currentX = e.clientX - rect.left;
      const currentY = e.clientY - rect.top;

      // 计算移动距离
      const deltaX = Math.abs(currentX - selectionState.startX);
      const deltaY = Math.abs(currentY - selectionState.startY);
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 只有移动距离超过阈值才开始框选
      if (!hasMovedRef.current && distance >= MINIMUM_DRAG_DISTANCE) {
        hasMovedRef.current = true;
        setSelectionState((prev) => ({
          ...prev,
          isSelecting: true,
        }));
      }

      if (hasMovedRef.current) {
        setSelectionState((prev) => ({
          ...prev,
          currentX,
          currentY,
        }));
      }
    };

    const handleGlobalMouseUp = () => {
      if (isDraggingRef.current) {
        // 如果有移动且正在选择，执行框选逻辑
        if (hasMovedRef.current && selectionState.isSelecting) {
          performSelection();
        }
        resetSelection();
      }
    };

    document.addEventListener("mousemove", handleGlobalMouseMove);
    document.addEventListener("mouseup", handleGlobalMouseUp);

    return () => {
      document.removeEventListener("mousemove", handleGlobalMouseMove);
      document.removeEventListener("mouseup", handleGlobalMouseUp);
    };
  }, [
    isDraggingRef.current,
    selectionState.startX,
    selectionState.startY,
    selectionState.isSelecting,
    containerRef,
    resetSelection,
    performSelection,
  ]);

  // 检查是否应该开始框选
  const shouldStartSelection = useCallback(
    (e: React.MouseEvent) => {
      // 在播放状态下不允许框选
      if (store.playing) {
        return false;
      }

      // 检查点击的目标是否是可以框选的区域
      const target = e.target as HTMLElement;

      // 如果点击的是时间线元素、轨道元素或其他交互元素，不开始框选
      if (
        target.closest(".timeline-element-container") ||
        target.closest(".timeline-indicator") ||
        target.closest(".caption-item") ||
        target.closest(".gap-indicator") ||
        target.closest("[data-testid]") ||
        target.closest("button") ||
        target.closest('[role="button"]') ||
        target.closest(".timeline-scale-markers")
      ) {
        return false;
      }

      return true;
    },
    [store.playing]
  );

  // 处理鼠标按下事件
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      // 只处理左键点击
      if (e.button !== 0) {
        return;
      }

      if (!shouldStartSelection(e) || !containerRef.current) {
        return;
      }

      e.preventDefault();
      e.stopPropagation();

      const rect = containerRef.current.getBoundingClientRect();
      const startX = e.clientX - rect.left;
      const startY = e.clientY - rect.top;

      setSelectionState({
        isSelecting: false, // 先不设为true，等到移动一定距离后再开始
        startX,
        startY,
        currentX: startX,
        currentY: startY,
      });

      isDraggingRef.current = true;
      hasMovedRef.current = false;
    },
    [shouldStartSelection, containerRef]
  );

  // 处理鼠标移动事件
  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isDraggingRef.current || !containerRef.current) {
        return;
      }

      const rect = containerRef.current.getBoundingClientRect();
      const currentX = e.clientX - rect.left;
      const currentY = e.clientY - rect.top;

      // 计算移动距离
      const deltaX = Math.abs(currentX - selectionState.startX);
      const deltaY = Math.abs(currentY - selectionState.startY);
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // 只有移动距离超过阈值才开始框选
      if (!hasMovedRef.current && distance >= MINIMUM_DRAG_DISTANCE) {
        hasMovedRef.current = true;
        setSelectionState((prev) => ({
          ...prev,
          isSelecting: true,
        }));
      }

      if (hasMovedRef.current) {
        setSelectionState((prev) => ({
          ...prev,
          currentX,
          currentY,
        }));
      }
    },
    [selectionState.startX, selectionState.startY, containerRef]
  );

  // 处理鼠标释放事件
  const handleMouseUp = useCallback(() => {
    if (!isDraggingRef.current) {
      return;
    }

    // 如果有移动且正在选择，执行框选逻辑
    if (hasMovedRef.current && selectionState.isSelecting) {
      performSelection();
    }

    resetSelection();
  }, [selectionState.isSelecting, performSelection, resetSelection]);

  return {
    selectionState,
    isSelecting: selectionState.isSelecting,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    resetSelection,
  };
};
